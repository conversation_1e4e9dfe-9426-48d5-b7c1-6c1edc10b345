# "你说我改" 功能 MySQL 数据库设计

## 1. 数据库表结构

### 1.1 用户表 (users)
```sql
CREATE TABLE `users` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户唯一标识',
  `username` VARCHAR(50) NOT NULL COMMENT '用户名',
  `email` VARCHAR(100) UNIQUE COMMENT '邮箱',
  `avatar_url` VARCHAR(255) COMMENT '头像URL',
  `points` INT DEFAULT 0 COMMENT '用户积分',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

### 1.2 建议分类表 (suggestion_categories)
```sql
CREATE TABLE `suggestion_categories` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '分类唯一标识',
  `name` VARCHAR(50) NOT NULL COMMENT '分类名称',
  `description` VARCHAR(255) COMMENT '分类描述',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='建议分类表';
```

### 1.3 建议表 (suggestions)
```sql
CREATE TABLE `suggestions` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '建议唯一标识',
  `title` VARCHAR(200) NOT NULL COMMENT '建议标题',
  `content` TEXT NOT NULL COMMENT '建议内容',
  `category_id` BIGINT UNSIGNED NOT NULL COMMENT '建议分类ID',
  `author_id` BIGINT UNSIGNED NOT NULL COMMENT '作者ID',
  `status` ENUM('pending', 'under_review', 'accepted', 'rejected', 'implemented') DEFAULT 'pending' COMMENT '状态（pending:待处理, under_review:审核中, accepted:已采纳, rejected:已拒绝, implemented:已实现）',
  `upvotes` INT DEFAULT 0 COMMENT '点赞数',
  `comments_count` INT DEFAULT 0 COMMENT '评论数',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_author_id` (`author_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_suggestions_author` FOREIGN KEY (`author_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_suggestions_category` FOREIGN KEY (`category_id`) REFERENCES `suggestion_categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='建议表';
```

### 1.4 建议媒体文件表 (suggestion_media)
```sql
CREATE TABLE `suggestion_media` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '媒体文件唯一标识',
  `suggestion_id` BIGINT UNSIGNED NOT NULL COMMENT '建议ID',
  `media_url` VARCHAR(255) NOT NULL COMMENT '媒体文件URL',
  `media_type` ENUM('image', 'video') NOT NULL COMMENT '媒体类型',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_suggestion_id` (`suggestion_id`),
  CONSTRAINT `fk_media_suggestion` FOREIGN KEY (`suggestion_id`) REFERENCES `suggestions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='建议媒体文件表';
```

### 1.5 评论表 (comments)
```sql
CREATE TABLE `comments` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '评论唯一标识',
  `suggestion_id` BIGINT UNSIGNED NOT NULL COMMENT '关联建议ID',
  `author_id` BIGINT UNSIGNED NOT NULL COMMENT '作者ID',
  `content` TEXT NOT NULL COMMENT '评论内容',
  `parent_id` BIGINT UNSIGNED DEFAULT NULL COMMENT '父评论ID（用于回复）',
  `upvotes` INT DEFAULT 0 COMMENT '点赞数',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_suggestion_id` (`suggestion_id`),
  KEY `idx_author_id` (`author_id`),
  KEY `idx_parent_id` (`parent_id`),
  CONSTRAINT `fk_comments_suggestion` FOREIGN KEY (`suggestion_id`) REFERENCES `suggestions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_comments_author` FOREIGN KEY (`author_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_comments_parent` FOREIGN KEY (`parent_id`) REFERENCES `comments` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';
```

### 1.6 建议点赞记录表 (suggestion_upvotes)
```sql
CREATE TABLE `suggestion_upvotes` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '点赞记录唯一标识',
  `suggestion_id` BIGINT UNSIGNED NOT NULL COMMENT '建议ID',
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_suggestion_user` (`suggestion_id`, `user_id`),
  KEY `idx_suggestion_id` (`suggestion_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_upvotes_suggestion` FOREIGN KEY (`suggestion_id`) REFERENCES `suggestions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_upvotes_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='建议点赞记录表';
```

### 1.7 评论点赞记录表 (comment_upvotes)
```sql
CREATE TABLE `comment_upvotes` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '点赞记录唯一标识',
  `comment_id` BIGINT UNSIGNED NOT NULL COMMENT '评论ID',
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_comment_user` (`comment_id`, `user_id`),
  KEY `idx_comment_id` (`comment_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_comment_upvotes_comment` FOREIGN KEY (`comment_id`) REFERENCES `comments` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_comment_upvotes_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论点赞记录表';
```

### 1.8 积分记录表 (point_records)
```sql
CREATE TABLE `point_records` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录唯一标识',
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
  `amount` INT NOT NULL COMMENT '积分变动数量',
  `reason` VARCHAR(100) NOT NULL COMMENT '变动原因',
  `related_id` BIGINT UNSIGNED COMMENT '关联ID（如建议ID、评论ID等）',
  `related_type` ENUM('suggestion', 'comment') COMMENT '关联类型',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_point_records_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分记录表';
```

## 2. 表关系图

```
users
├── suggestions (author_id)
├── comments (author_id)
├── suggestion_upvotes (user_id)
├── comment_upvotes (user_id)
└── point_records (user_id)

suggestion_categories
└── suggestions (category_id)

suggestions
├── suggestion_media (suggestion_id)
├── comments (suggestion_id)
└── suggestion_upvotes (suggestion_id)

comments
├── comments (parent_id)
├── comment_upvotes (comment_id)
└── point_records (related_id, when related_type='comment')
```

## 3. 索引优化建议

1. 对于经常查询的字段建立索引，如 `suggestions.category_id`、`suggestions.status`、`comments.suggestion_id` 等
2. 对于关联查询的外键字段建立索引
3. 对于时间范围查询的字段建立索引，如 `created_at`

## 4. 数据初始化建议

```sql
-- 初始化建议分类
INSERT INTO `suggestion_categories` (`name`, `description`) VALUES
('功能建议', '对产品功能的改进建议'),
('界面优化', '对产品界面的优化建议'),
('性能优化', '对产品性能的优化建议'),
('Bug反馈', '产品使用中发现的Bug反馈'),
('其他', '其他类型的建议');
```

## 5. 注意事项

1. 所有时间字段使用 TIMESTAMP 类型，便于时区处理
2. 使用 BIGINT UNSIGNED 作为主键，满足大量数据需求
3. 使用 ENUM 类型限制状态字段的取值范围
4. 添加适当的外键约束保证数据一致性
5. 对于频繁更新的字段（如 upvotes），考虑使用缓存优化