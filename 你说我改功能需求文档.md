# "你说我改" 功能需求文档

## 1. 概述
"你说我改" 是一个用户反馈系统，用户可以提交建议、查看他人建议、参与讨论，并获得积分奖励。

## 2. 功能模块

### 2.1 首页展示
- 展示热门建议列表
- 展示最新建议列表
- 展示推荐建议

### 2.2 建议列表
- 建议分类浏览
- 按时间排序（最新）
- 搜索功能

### 2.3 建议详情
- 建议内容展示
- 用户评论和回复功能
- 点赞/支持功能
- 截图上传和展示

### 2.4 提交建议
- 建议表单（标题、内容、分类）
- 图片/视频上传功能
- 提交成功提示

### 2.5 用户积分系统
- 积分获取规则
- 积分使用方式
- 积分记录查询

## 3. 数据模型

### 3.1 建议 (Suggestion)
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 建议唯一标识 |
| title | string | 是 | 建议标题 |
| content | string | 是 | 建议内容 |
| category | string | 是 | 建议分类 |
| author_id | string | 是 | 作者ID |
| created_at | datetime | 是 | 创建时间 |
| updated_at | datetime | 是 | 更新时间 |
| status | string | 是 | 状态 (pending, approved, rejected, implemented) |
| upvotes | integer | 否 | 点赞数 |
| comments_count | integer | 否 | 评论数 |
| media_urls | array | 否 | 媒体文件URL列表 |

### 3.2 评论 (Comment)
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 评论唯一标识 |
| suggestion_id | string | 是 | 关联建议ID |
| author_id | string | 是 | 作者ID |
| content | string | 是 | 评论内容 |
| parent_id | string | 否 | 父评论ID（用于回复） |
| created_at | datetime | 是 | 创建时间 |
| upvotes | integer | 否 | 点赞数 |

### 3.3 用户 (User)
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 用户唯一标识 |
| username | string | 是 | 用户名 |
| points | integer | 否 | 用户积分 |
| avatar_url | string | 否 | 头像URL |

### 3.4 积分记录 (PointRecord)
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 记录唯一标识 |
| user_id | string | 是 | 用户ID |
| amount | integer | 是 | 积分变动数量 |
| reason | string | 是 | 变动原因 |
| created_at | datetime | 是 | 创建时间 |

## 4. API 接口设计

### 4.1 建议相关接口

#### 获取建议列表
```
GET /api/suggestions
参数:
- page: 页码
- limit: 每页数量
- category: 分类筛选
- sort: 排序方式 (newest, hottest)
返回: 建议列表
```

#### 获取建议详情
```
GET /api/suggestions/{id}
返回: 建议详情信息
```

#### 创建建议
```
POST /api/suggestions
参数:
- title: 标题
- content: 内容
- category: 分类
- media_urls: 媒体URL列表
返回: 创建的建议信息
```

#### 点赞建议
```
POST /api/suggestions/{id}/upvote
返回: 更新后的建议信息
```

### 4.2 评论相关接口

#### 获取评论列表
```
GET /api/suggestions/{id}/comments
参数:
- page: 页码
- limit: 每页数量
返回: 评论列表
```

#### 发表评论
```
POST /api/suggestions/{id}/comments
参数:
- content: 评论内容
- parent_id: 父评论ID（可选）
返回: 创建的评论信息
```

#### 点赞评论
```
POST /api/comments/{id}/upvote
返回: 更新后的评论信息
```

### 4.3 用户相关接口

#### 获取用户信息
```
GET /api/users/{id}
返回: 用户信息
```

#### 获取用户积分记录
```
GET /api/users/{id}/points
参数:
- page: 页码
- limit: 每页数量
返回: 积分记录列表
```

## 5. 业务逻辑

### 5.1 积分规则
- 提交建议：+10分
- 建议被点赞：+1分/次
- 评论被点赞：+1分/次
- 建议被采纳：+50分

### 5.2 建议状态流程
1. pending（待审核）
2. approved（已通过）
3. rejected（已拒绝）
4. implemented（已实现）

## 6. 权限控制
- 所有用户可浏览建议
- 登录用户可提交建议、评论、点赞
- 管理员可审核建议、修改状态